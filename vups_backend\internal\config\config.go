package config

import (
	"fmt"
	"os"
	"time"

	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	Server    ServerConfig    `mapstructure:"server"`
	Redis     RedisConfig     `mapstructure:"redis"`
	Celery    CeleryConfig    `mapstructure:"celery"`
	Python    PythonConfig    `mapstructure:"python"`
	Logging   LoggingConfig   `mapstructure:"logging"`
	RateLimit RateLimitConfig `mapstructure:"rate_limit"`
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	Mode         string        `mapstructure:"mode"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
	IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
	CORS         CORSConfig    `mapstructure:"cors"`
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	Password     string        `mapstructure:"password"`
	DB           int           `mapstructure:"db"`
	PoolSize     int           `mapstructure:"pool_size"`
	MinIdleConns int           `mapstructure:"min_idle_conns"`
	DialTimeout  time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
	IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
}

// CeleryConfig holds Celery configuration
type CeleryConfig struct {
	Broker      string        `mapstructure:"broker"`
	Backend     string        `mapstructure:"backend"`
	TaskTimeout time.Duration `mapstructure:"task_timeout"`
	RetryDelay  time.Duration `mapstructure:"retry_delay"`
	MaxRetries  int           `mapstructure:"max_retries"`
}

// PythonConfig holds Python execution configuration
type PythonConfig struct {
	Executable string        `mapstructure:"executable"`
	ScriptPath string        `mapstructure:"script_path"`
	Timeout    time.Duration `mapstructure:"timeout"`
	MaxWorkers int           `mapstructure:"max_workers"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	Output     string `mapstructure:"output"`
	Filename   string `mapstructure:"filename"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
	Compress   bool   `mapstructure:"compress"`
}

// CORSConfig holds CORS configuration
type CORSConfig struct {
	AllowOrigins     []string      `mapstructure:"allow_origins"`
	AllowMethods     []string      `mapstructure:"allow_methods"`
	AllowHeaders     []string      `mapstructure:"allow_headers"`
	ExposeHeaders    []string      `mapstructure:"expose_headers"`
	AllowCredentials bool          `mapstructure:"allow_credentials"`
	MaxAge           time.Duration `mapstructure:"max_age"`
}

// RateLimitConfig holds rate limiting configuration
type RateLimitConfig struct {
	Enabled    bool          `mapstructure:"enabled"`
	Rate       float64       `mapstructure:"rate"`
	Burst      int           `mapstructure:"burst"`
	WindowSize time.Duration `mapstructure:"window_size"`
}

// Load loads configuration from file and environment variables
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("../configs")
	viper.AddConfigPath("../../configs")

	// Set default values
	setDefaults()

	// Enable environment variable support
	viper.AutomaticEnv()
	viper.SetEnvPrefix("VUPS")

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Override with environment variables if present
	overrideWithEnv(&config)

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.mode", "release")
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "120s")

	// CORS defaults
	viper.SetDefault("server.cors.allow_origins", []string{"*"})
	viper.SetDefault("server.cors.allow_methods", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
	viper.SetDefault("server.cors.allow_headers", []string{"*"})
	viper.SetDefault("server.cors.allow_credentials", true)
	viper.SetDefault("server.cors.max_age", "12h")

	// Redis defaults
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("redis.pool_size", 10)
	viper.SetDefault("redis.min_idle_conns", 5)
	viper.SetDefault("redis.dial_timeout", "5s")
	viper.SetDefault("redis.read_timeout", "3s")
	viper.SetDefault("redis.write_timeout", "3s")
	viper.SetDefault("redis.idle_timeout", "300s")

	// Celery defaults
	viper.SetDefault("celery.broker", "redis://localhost:6379/0")
	viper.SetDefault("celery.backend", "redis://localhost:6379/0")
	viper.SetDefault("celery.task_timeout", "300s")
	viper.SetDefault("celery.retry_delay", "5s")
	viper.SetDefault("celery.max_retries", 3)

	// Python defaults
	viper.SetDefault("python.executable", "python")
	viper.SetDefault("python.script_path", "./")
	viper.SetDefault("python.timeout", "300s")
	viper.SetDefault("python.max_workers", 5)

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")
	viper.SetDefault("logging.max_size", 100)
	viper.SetDefault("logging.max_backups", 3)
	viper.SetDefault("logging.max_age", 28)
	viper.SetDefault("logging.compress", true)

	// Rate limit defaults
	viper.SetDefault("rate_limit.enabled", true)
	viper.SetDefault("rate_limit.rate", 100.0)
	viper.SetDefault("rate_limit.burst", 200)
	viper.SetDefault("rate_limit.window_size", "1m")
}

// overrideWithEnv overrides config with environment variables
func overrideWithEnv(config *Config) {
	if host := os.Getenv("VUPS_SERVER_HOST"); host != "" {
		config.Server.Host = host
	}
	if port := os.Getenv("VUPS_SERVER_PORT"); port != "" {
		viper.Set("server.port", port)
	}
	if redisHost := os.Getenv("VUPS_REDIS_HOST"); redisHost != "" {
		config.Redis.Host = redisHost
	}
	if redisPassword := os.Getenv("VUPS_REDIS_PASSWORD"); redisPassword != "" {
		config.Redis.Password = redisPassword
	}
	if pythonPath := os.Getenv("VUPS_PYTHON_EXECUTABLE"); pythonPath != "" {
		config.Python.Executable = pythonPath
	}
	if scriptPath := os.Getenv("VUPS_PYTHON_SCRIPT_PATH"); scriptPath != "" {
		config.Python.ScriptPath = scriptPath
	}
}

// GetAddr returns the server address
func (c *Config) GetAddr() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}

// GetRedisAddr returns the Redis address
func (c *Config) GetRedisAddr() string {
	return fmt.Sprintf("%s:%d", c.Redis.Host, c.Redis.Port)
}
