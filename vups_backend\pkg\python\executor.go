package python

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os/exec"
	"strings"
	"sync"
	"time"

	"vups_backend/internal/config"
)

// Executor handles Python script execution
type Executor struct {
	config     *config.PythonConfig
	workerPool chan struct{}
	mu         sync.RWMutex
}

// ExecutionResult represents the result of a Python script execution
type ExecutionResult struct {
	Success  bool          `json:"success"`
	Data     interface{}   `json:"data"`
	Error    string        `json:"error,omitempty"`
	Duration time.Duration `json:"duration"`
	ExitCode int           `json:"exit_code"`
}

// StreamCallback is called for each line of streaming output
type StreamCallback func(line string) error

// NewExecutor creates a new Python executor
func NewExecutor(cfg *config.PythonConfig) *Executor {
	return &Executor{
		config:     cfg,
		workerPool: make(chan struct{}, cfg.MaxWorkers),
	}
}

// Execute runs a Python script with arguments
func (e *Executor) Execute(ctx context.Context, script string, args []string) (*ExecutionResult, error) {
	// Acquire worker slot
	select {
	case e.workerPool <- struct{}{}:
		defer func() { <-e.workerPool }()
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	start := time.Now()

	// Create command
	cmdArgs := append([]string{script}, args...)
	cmd := exec.CommandContext(ctx, e.config.Executable, cmdArgs...)
	cmd.Dir = e.config.ScriptPath

	// Set environment variables to ensure UTF-8 encoding
	cmd.Env = append(cmd.Env,
		"PYTHONIOENCODING=utf-8",
		"PYTHONLEGACYWINDOWSSTDIO=utf-8",
		"LC_ALL=C.UTF-8",
		"LANG=C.UTF-8",
	)

	// Set up pipes
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	stderr, err := cmd.StderrPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// Start command
	if err := cmd.Start(); err != nil {
		return nil, fmt.Errorf("failed to start command: %w", err)
	}

	// Read output
	var stdout_data, stderr_data strings.Builder
	var wg sync.WaitGroup

	wg.Add(2)
	go func() {
		defer wg.Done()
		io.Copy(&stdout_data, stdout)
	}()

	go func() {
		defer wg.Done()
		io.Copy(&stderr_data, stderr)
	}()

	// Wait for command to complete
	err = cmd.Wait()
	wg.Wait()

	duration := time.Since(start)
	exitCode := cmd.ProcessState.ExitCode()

	result := &ExecutionResult{
		Duration: duration,
		ExitCode: exitCode,
	}

	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("command failed: %v, stderr: %s", err, stderr_data.String())
		return result, nil
	}

	// Parse JSON output if possible
	output := strings.TrimSpace(stdout_data.String())
	if output != "" {
		var data interface{}
		if err := json.Unmarshal([]byte(output), &data); err == nil {
			result.Data = data
		} else {
			result.Data = output
		}
	}

	result.Success = true
	return result, nil
}

// ExecuteStream runs a Python script with streaming output
func (e *Executor) ExecuteStream(ctx context.Context, script string, args []string, callback StreamCallback) (*ExecutionResult, error) {
	// Acquire worker slot
	select {
	case e.workerPool <- struct{}{}:
		defer func() { <-e.workerPool }()
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	start := time.Now()

	// Create command
	cmdArgs := append([]string{script}, args...)
	cmd := exec.CommandContext(ctx, e.config.Executable, cmdArgs...)
	cmd.Dir = e.config.ScriptPath

	// Set environment variables to ensure UTF-8 encoding
	cmd.Env = append(cmd.Env,
		"PYTHONIOENCODING=utf-8",
		"PYTHONLEGACYWINDOWSSTDIO=utf-8",
		"LC_ALL=C.UTF-8",
		"LANG=C.UTF-8",
	)

	// Set up pipes
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	stderr, err := cmd.StderrPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// Start command
	if err := cmd.Start(); err != nil {
		return nil, fmt.Errorf("failed to start command: %w", err)
	}

	// Stream stdout
	var stderr_data strings.Builder
	var wg sync.WaitGroup

	wg.Add(2)

	// Handle stdout streaming
	go func() {
		defer wg.Done()
		scanner := bufio.NewScanner(stdout)
		for scanner.Scan() {
			line := scanner.Text()
			if callback != nil {
				if err := callback(line); err != nil {
					break
				}
			}
		}
	}()

	// Handle stderr
	go func() {
		defer wg.Done()
		io.Copy(&stderr_data, stderr)
	}()

	// Wait for command to complete
	err = cmd.Wait()
	wg.Wait()

	duration := time.Since(start)
	exitCode := cmd.ProcessState.ExitCode()

	result := &ExecutionResult{
		Duration: duration,
		ExitCode: exitCode,
	}

	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("command failed: %v, stderr: %s", err, stderr_data.String())
		return result, nil
	}

	result.Success = true
	return result, nil
}

// VUPSearchExecutor executes VUP search queries
func (e *Executor) VUPSearchExecutor(ctx context.Context, question string) (*ExecutionResult, error) {
	script := "scripts/vup_search_wrapper.py"
	args := []string{question}
	return e.Execute(ctx, script, args)
}

// VUPSearchStream executes VUP search with streaming
func (e *Executor) VUPSearchStream(ctx context.Context, question string, callback StreamCallback) (*ExecutionResult, error) {
	script := "scripts/vup_search_stream_wrapper.py"
	args := []string{question}
	return e.ExecuteStream(ctx, script, args, callback)
}

// LiveInfoQuery executes live info queries
func (e *Executor) LiveInfoQuery(ctx context.Context, roomID, startTime, endTime, dataType string) (*ExecutionResult, error) {
	script := "scripts/live_info_wrapper.py"
	args := []string{roomID, startTime, endTime, dataType}
	return e.Execute(ctx, script, args)
}

// UserDataQuery executes user data queries
func (e *Executor) UserDataQuery(ctx context.Context, uid, startTime, endTime, dataType string) (*ExecutionResult, error) {
	script := "scripts/user_data_wrapper.py"
	args := []string{uid, startTime, endTime, dataType}
	return e.Execute(ctx, script, args)
}

// Close cleans up the executor
func (e *Executor) Close() error {
	// Wait for all workers to finish
	for i := 0; i < cap(e.workerPool); i++ {
		e.workerPool <- struct{}{}
	}
	close(e.workerPool)
	return nil
}
