"""
Board query service module.
Handles dahanghai flow analysis, board statistics, and related board data queries.
"""

import time
from collections import defaultdict
from datetime import datetime
from typing import Dict, List, Optional

from vups.logger import logger
from vups_server.base.query_base import BaseQueryService


class BoardQueryService(BaseQueryService):
    """Service for board-related queries including flow analysis and statistics."""

    def __init__(self):
        super().__init__(cache_ttl=600)  # 10 minutes cache for board data


    async def get_target_flow_analysis(
        self,
        target_vtube_name: str,
        current_time_str: str,
        previous_time_str: str,
        n: int = 5
    ) -> Optional[Dict]:
        """
        Analyze user flow for a target vtuber between two time points.

        Args:
            target_vtube_name: Name of the target vtuber
            current_time_str: Current time point as string
            previous_time_str: Previous time point as string
            n: Number of top flow sources/targets to return

        Returns:
            Dictionary containing inflow and outflow data, or None if error occurred
        """
        cache_key = f"target_flow_{target_vtube_name}_{current_time_str}_{previous_time_str}_{n}"

        flow_analysis_query = """
            WITH target_current AS (
                SELECT uid, username, face
                FROM dahanghai_list_table
                WHERE time::text = $2 AND up_name = $1
            ),
            target_previous AS (
                SELECT uid, username, face
                FROM dahanghai_list_table
                WHERE time::text = $3 AND up_name = $1
            ),
            all_current AS (
                SELECT up_name, uid, username, face
                FROM dahanghai_list_table
                WHERE time::text = $2 AND up_name != $1
            ),
            all_previous AS (
                SELECT up_name, uid, username, face
                FROM dahanghai_list_table
                WHERE time::text = $3 AND up_name != $1
            ),
            -- Users who flowed IN to target (in current but not in previous)
            flowed_in_users AS (
                SELECT tc.uid, tc.username, tc.face
                FROM target_current tc
                LEFT JOIN target_previous tp ON tc.uid = tp.uid
                WHERE tp.uid IS NULL
            ),
            -- Users who flowed OUT from target (in previous but not in current)
            flowed_out_users AS (
                SELECT tp.uid, tp.username, tp.face
                FROM target_previous tp
                LEFT JOIN target_current tc ON tp.uid = tc.uid
                WHERE tc.uid IS NULL
            ),
            -- Find where flowed_in users came from
            inflow_sources AS (
                SELECT
                    ap.up_name,
                    COUNT(*) as user_count,
                    ARRAY_AGG(ARRAY[ap.uid::text, ap.username, ap.face]) as users
                FROM flowed_in_users fiu
                INNER JOIN all_previous ap ON fiu.uid = ap.uid
                GROUP BY ap.up_name
                ORDER BY user_count DESC
                LIMIT $4
            ),
            -- Find where flowed_out users went to
            outflow_targets AS (
                SELECT
                    ac.up_name,
                    COUNT(*) as user_count,
                    ARRAY_AGG(ARRAY[ac.uid::text, ac.username, ac.face]) as users
                FROM flowed_out_users fou
                INNER JOIN all_current ac ON fou.uid = ac.uid
                GROUP BY ac.up_name
                ORDER BY user_count DESC
                LIMIT $4
            )
            SELECT
                'in' as flow_type, up_name, users
            FROM inflow_sources
            UNION ALL
            SELECT
                'out' as flow_type, up_name, users
            FROM outflow_targets
        """

        flow_results = await self._cached_query(
            cache_key=cache_key,
            query=flow_analysis_query,
            params=[target_vtube_name, current_time_str, previous_time_str, n],
            fetch_type="fetch"
        )

        if flow_results is None:
            return None

        # Process results into expected format
        result = {'in': {}, 'out': {}}

        for row in flow_results:
            flow_type = row['flow_type']
            up_name = row['up_name']
            users = row['users']

            # Convert array format to expected tuple format
            user_tuples = []
            for user_array in users:
                if len(user_array) >= 3:
                    user_tuples.append((int(user_array[0]), user_array[1], user_array[2]))

            result[flow_type][up_name] = user_tuples

        return result

    async def get_target_flow_analysis_by_period(
        self,
        target_vtube_name: str,
        start_time_str1: str,
        end_time_str1: str,
        start_time_str2: str,
        end_time_str2: str,
        n: int = 5
    ) -> Optional[Dict]:
        """
        Analyze user flow for a target vtuber between two time periods.

        Args:
            target_vtube_name: Name of the target vtuber
            start_time_str1: Start date of first period (YYYY-MM-DD)
            end_time_str1: End date of first period (YYYY-MM-DD)
            start_time_str2: Start date of second period (YYYY-MM-DD)
            end_time_str2: End date of second period (YYYY-MM-DD)
            n: Number of top flow sources/targets to return

        Returns:
            Dictionary containing inflow and outflow data, or None if error occurred
        """
        cache_key = f"target_flow_period_{target_vtube_name}_{start_time_str1}_{end_time_str1}_{start_time_str2}_{end_time_str2}_{n}"

        try:
            # Convert date strings to datetime.date objects
            start_date1 = datetime.strptime(start_time_str1, '%Y-%m-%d').date()
            end_date1 = datetime.strptime(end_time_str1, '%Y-%m-%d').date()
            start_date2 = datetime.strptime(start_time_str2, '%Y-%m-%d').date()
            end_date2 = datetime.strptime(end_time_str2, '%Y-%m-%d').date()

            period_flow_query = """
                WITH target_period1 AS (
                    SELECT DISTINCT uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $2 AND $3
                    AND up_name = $1
                ),
                target_period2 AS (
                    SELECT DISTINCT uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $4 AND $5
                    AND up_name = $1
                ),
                all_period1 AS (
                    SELECT DISTINCT up_name, uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $2 AND $3
                    AND up_name != $1
                ),
                all_period2 AS (
                    SELECT DISTINCT up_name, uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $4 AND $5
                    AND up_name != $1
                ),
                -- Users who flowed IN to target (in period2 but not in period1)
                flowed_in_users AS (
                    SELECT tp2.uid, tp2.username, tp2.face
                    FROM target_period2 tp2
                    LEFT JOIN target_period1 tp1 ON tp2.uid = tp1.uid
                    WHERE tp1.uid IS NULL
                ),
                -- Users who flowed OUT from target (in period1 but not in period2)
                flowed_out_users AS (
                    SELECT tp1.uid, tp1.username, tp1.face
                    FROM target_period1 tp1
                    LEFT JOIN target_period2 tp2 ON tp1.uid = tp2.uid
                    WHERE tp2.uid IS NULL
                ),
                -- Find where flowed_in users came from
                inflow_sources AS (
                    SELECT
                        ap1.up_name,
                        COUNT(*) as user_count,
                        ARRAY_AGG(ARRAY[ap1.uid::text, ap1.username, ap1.face]) as users
                    FROM flowed_in_users fiu
                    INNER JOIN all_period1 ap1 ON fiu.uid = ap1.uid
                    GROUP BY ap1.up_name
                    ORDER BY user_count DESC
                    LIMIT $6
                ),
                -- Find where flowed_out users went to
                outflow_targets AS (
                    SELECT
                        ap2.up_name,
                        COUNT(*) as user_count,
                        ARRAY_AGG(ARRAY[ap2.uid::text, ap2.username, ap2.face]) as users
                    FROM flowed_out_users fou
                    INNER JOIN all_period2 ap2 ON fou.uid = ap2.uid
                    GROUP BY ap2.up_name
                    ORDER BY user_count DESC
                    LIMIT $6
                )
                SELECT
                    'in' as flow_type, up_name, users
                FROM inflow_sources
                UNION ALL
                SELECT
                    'out' as flow_type, up_name, users
                FROM outflow_targets
            """

            flow_results = await self._cached_query(
                cache_key=cache_key,
                query=period_flow_query,
                params=[target_vtube_name, start_date1, end_date1, start_date2, end_date2, n],
                fetch_type="fetch"
            )

            if flow_results is None:
                return None

            # Process results into expected format
            result = {'in': {}, 'out': {}}

            for row in flow_results:
                flow_type = row['flow_type']
                up_name = row['up_name']
                users = row['users']

                # Convert array format to expected tuple format
                user_tuples = []
                for user_array in users:
                    if len(user_array) >= 3:
                        user_tuples.append((int(user_array[0]), user_array[1], user_array[2]))

                result[flow_type][up_name] = user_tuples

            return result

        except ValueError as e:
            logger.error(f"Invalid date format in period flow analysis: {e}")
            return None
        except Exception as e:
            logger.error(f"Error in period flow analysis: {e}")
            return None

    async def get_dahanghai_table_stats(self) -> Dict:
        """
        Get statistics about dahanghai_list_table for monitoring.

        Returns:
            Dictionary containing table statistics
        """
        cache_key = "dahanghai_table_stats"

        stats_query = """
            SELECT
                COUNT(*) as total_records,
                COUNT(DISTINCT up_name) as unique_vtubers,
                COUNT(DISTINCT uid) as unique_users,
                MIN(time) as earliest_date,
                MAX(time) as latest_date
            FROM dahanghai_list_table
        """

        stats_result = await self._cached_query(
            cache_key=cache_key,
            query=stats_query,
            params=[],
            fetch_type="fetchrow"
        )

        if stats_result:
            return dict(stats_result)
        return {}

    async def get_whole_flow_from_all_vtubers(
        self,
        current_time_str: str,
        previous_time_str: str
    ) -> Optional[Dict]:
        """
        Analyze user flow between all vtubers for two time points.

        Args:
            current_time_str: Current time point as string (YYYY-MM-DD)
            previous_time_str: Previous time point as string (YYYY-MM-DD)

        Returns:
            Dictionary containing loss, p2c flow, and additional users data
        """
        cache_key = f"whole_flow_{current_time_str}_{previous_time_str}"

        # SQL to fetch dahanghai users for a given time
        dahanghai_users_query = """
            SELECT up_uid, up_name, uid, username, face
            FROM dahanghai_list_table
            WHERE time::text = $1
        """

        # Get current time data
        current_results = await self._cached_query(
            cache_key=f"{cache_key}_current",
            query=dahanghai_users_query,
            params=[current_time_str],
            fetch_type="fetch"
        )

        # Get previous time data
        previous_results = await self._cached_query(
            cache_key=f"{cache_key}_previous",
            query=dahanghai_users_query,
            params=[previous_time_str],
            fetch_type="fetch"
        )

        if current_results is None or previous_results is None:
            return None

        # Process data using the same logic as the original function
        current_time_users_by_anchor = defaultdict(set)
        for rec in current_results:
            user_tuple = (rec['uid'], rec['username'], rec['face'])
            current_time_users_by_anchor[rec['up_name']].add(user_tuple)

        previous_time_users_by_anchor = defaultdict(set)
        for rec in previous_results:
            user_tuple = (rec['uid'], rec['username'], rec['face'])
            previous_time_users_by_anchor[rec['up_name']].add(user_tuple)

        direct_loss = defaultdict(set)
        p2c_flow_between_anchors = defaultdict(lambda: defaultdict(set))

        all_current_users_set = set()
        for users_set in current_time_users_by_anchor.values():
            all_current_users_set.update(users_set)

        for prev_anchor_name, prev_users_set in previous_time_users_by_anchor.items():
            for user_tuple in prev_users_set:
                found_in_current = False
                for curr_anchor_name, curr_users_set in current_time_users_by_anchor.items():
                    if user_tuple in curr_users_set:
                        if prev_anchor_name != curr_anchor_name:
                            p2c_flow_between_anchors[prev_anchor_name][curr_anchor_name].add(user_tuple)
                        found_in_current = True
                        break
                if not found_in_current:
                    direct_loss[prev_anchor_name].add(user_tuple)

        additional_users = defaultdict(set)
        all_previous_users_set = set()
        for users_set in previous_time_users_by_anchor.values():
            all_previous_users_set.update(users_set)

        for curr_anchor_name, curr_users_set in current_time_users_by_anchor.items():
            for user_tuple in curr_users_set:
                if user_tuple not in all_previous_users_set:
                    additional_users[curr_anchor_name].add(user_tuple)

        return {
            'loss': {k: list(v) for k, v in direct_loss.items()},
            'p2c': {k_outer: {k_inner: list(v_inner) for k_inner, v_inner in v_outer.items()} for k_outer, v_outer in p2c_flow_between_anchors.items()},
            'add': {k: list(v) for k, v in additional_users.items()},
        }

    async def get_whole_info_from_all_vtubers(self) -> Optional[Dict]:
        """
        Get comprehensive information for all vtubers.

        Returns:
            Dictionary containing time and data with vtuber information
        """
        cache_key = "whole_vtuber_info"

        vtuber_info_query = """
            SELECT uid, name, face, sign, birthday, top_photo AS cover_url
            FROM user_info_table
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=vtuber_info_query,
            params=[],
            fetch_type="fetch"
        )

        if results is None:
            return None

        formatted_data = []
        for rec in results:
            formatted_data.append({
                'name': rec['name'],
                'uid': rec['uid'],
                'face': rec['face'],
                'sign': rec['sign'],
                'birthday': rec['birthday'],
                'cover_url': rec['cover_url']
            })

        return {
            'time': datetime.now().isoformat(),
            'data': formatted_data
        }

    async def get_all_dahanghai_and_follower_rise_num_data(
        self,
        recent_days: int = 1
    ) -> Optional[Dict]:
        """
        Get dahanghai and follower statistics with rise numbers for all vtubers.

        Args:
            recent_days: Number of days to look back for calculating rise numbers

        Returns:
            Dictionary containing time and data with statistics
        """
        cache_key = f"dahanghai_follower_rise_{recent_days}"

        rise_stats_query = """
            WITH LatestRecord AS (
                SELECT
                    uid,
                    name,
                    datetime,
                    dahanghai_num,
                    follower_num,
                    ROW_NUMBER() OVER (PARTITION BY uid ORDER BY datetime DESC) as rn
                FROM current_stat_table
            ),
            CurrentData AS (
                SELECT
                    uid,
                    name,
                    datetime,
                    dahanghai_num,
                    follower_num
                FROM LatestRecord
                WHERE rn = 1
            ),
            PastRecord AS (
                SELECT
                    cs.uid,
                    cs.datetime AS past_datetime,
                    cs.dahanghai_num AS past_dahanghai_num_val,
                    cs.follower_num AS past_follower_num_val,
                    ROW_NUMBER() OVER (PARTITION BY cs.uid ORDER BY cs.datetime DESC) as rn_past
                FROM current_stat_table cs
                JOIN CurrentData cd ON cs.uid = cd.uid
                WHERE cs.datetime <= (cd.datetime - MAKE_INTERVAL(days => $1))
            ),
            PastData AS (
                SELECT
                    uid,
                    past_dahanghai_num_val,
                    past_follower_num_val
                FROM PastRecord
                WHERE rn_past = 1
            )
            SELECT
                cd.name,
                cd.dahanghai_num,
                COALESCE(cd.dahanghai_num - pd.past_dahanghai_num_val, cd.dahanghai_num) AS dahanghai_rise_num,
                cd.follower_num,
                COALESCE(cd.follower_num - pd.past_follower_num_val, cd.follower_num) AS follower_rise_num
            FROM CurrentData cd
            LEFT JOIN PastData pd ON cd.uid = pd.uid
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=rise_stats_query,
            params=[recent_days],
            fetch_type="fetch"
        )

        if results is None:
            return None

        formatted_data = []
        for rec in results:
            formatted_data.append({
                'name': rec['name'],
                'dahanghai_num': rec['dahanghai_num'],
                'dahanghai_rise_num': rec['dahanghai_rise_num'],
                'follower_num': rec['follower_num'],
                'follower_rise_num': rec['follower_rise_num']
            })

        return {
            'time': datetime.now().isoformat(),
            'data': formatted_data
        }

    async def get_all_live_status_data(self) -> Optional[List[Dict]]:
        """
        Get current live status for all vtubers.

        Returns:
            List of dictionaries containing live status information
        """
        cache_key = "all_live_status"

        live_status_query = """
            SELECT DISTINCT ON (lsm.room_id)
                ui.name,
                CASE WHEN lsm.live_status = 1 THEN TRUE ELSE FALSE END AS if_live,
                ui.live_url,
                lsm.title AS live_title,
                lsm.cover AS live_cover,
                lsm.room_id AS live_roomid
            FROM live_status_minute_table lsm
            JOIN user_info_table ui ON lsm.room_id = ui.room_id
            WHERE lsm.datetime >= NOW() - INTERVAL '24 HOURS'
            ORDER BY lsm.room_id, lsm.datetime DESC
        """

        start_time = time.time()
        results = await self._cached_query(
            cache_key=cache_key,
            query=live_status_query,
            params=[],
            fetch_type="fetch",
            ttl=300  # 5 minutes cache for live status
        )
        query_time = time.time() - start_time

        logger.info(f"Live status query completed in {query_time:.3f}s, returned {len(results) if results else 0} records")

        if results is None:
            return None

        formatted_data = []
        for rec in results:
            formatted_data.append({
                'name': rec['name'],
                'if_live': rec['if_live'],
                'live_url': rec['live_url'],
                'live_title': rec['live_title'],
                'live_cover': rec['live_cover'],
                'live_roomid': rec['live_roomid']
            })

        return formatted_data

    async def get_all_dahanghai_and_follower_data(self) -> Optional[Dict]:
        """
        Get dahanghai and follower statistics for all vtubers (deprecated version).

        Note: This function is deprecated. Use get_all_dahanghai_and_follower_rise_num_data instead.

        Returns:
            Dictionary containing time and data with statistics
        """
        cache_key = "dahanghai_follower_legacy"

        legacy_stats_query = """
            WITH RankedStats AS (
                SELECT
                    uid,
                    name,
                    timestamp,
                    dahanghai_num,
                    follower_num,
                    LAG(dahanghai_num, 1, 0) OVER (PARTITION BY uid ORDER BY timestamp) as prev_dahanghai_num,
                    LAG(follower_num, 1, 0) OVER (PARTITION BY uid ORDER BY timestamp) as prev_follower_num,
                    ROW_NUMBER() OVER (PARTITION BY uid ORDER BY timestamp DESC) as rn
                FROM current_stat_table
            )
            SELECT
                uid,
                name,
                dahanghai_num,
                (dahanghai_num - prev_dahanghai_num) as dahanghai_rise_num,
                follower_num,
                (follower_num - prev_follower_num) as follower_rise_num
            FROM RankedStats
            WHERE rn = 1
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=legacy_stats_query,
            params=[],
            fetch_type="fetch"
        )

        if results is None:
            return None

        formatted_data = []
        for rec in results:
            formatted_data.append({
                'name': rec['name'],
                'dahanghai_num': rec['dahanghai_num'],
                'dahanghai_rise_num': rec['dahanghai_rise_num'],
                'follower_num': rec['follower_num'],
                'follower_rise_num': rec['follower_rise_num']
            })

        return {
            'time': datetime.now().isoformat(),
            'data': formatted_data
        }

    async def clear_cache(self) -> None:
        """Clear all cached data for this service."""
        await self.cache.clear()


# Global instance for easy access
board_query_service = BoardQueryService()


# ============================================================================
# BACKWARD COMPATIBILITY FUNCTIONS
# ============================================================================

async def collect_target_flow_with_target_vtuber_optimized(
    target_vtube_name: str,
    current_time_str: str,
    previous_time_str: str,
    n: int = 5
):
    """
    DEPRECATED: Use BoardQueryService.get_target_flow_analysis() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_target_flow_analysis(
        target_vtube_name, current_time_str, previous_time_str, n
    )


async def collect_target_flow_with_target_vtuber_by_period_optimized(
    target_vtube_name: str,
    start_time_str1: str,
    end_time_str1: str,
    start_time_str2: str,
    end_time_str2: str,
    n: int = 5
):
    """
    DEPRECATED: Use BoardQueryService.get_target_flow_analysis_by_period() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_target_flow_analysis_by_period(
        target_vtube_name, start_time_str1, end_time_str1,
        start_time_str2, end_time_str2, n
    )


async def get_dahanghai_table_stats():
    """
    DEPRECATED: Use BoardQueryService.get_dahanghai_table_stats() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_dahanghai_table_stats()


def clear_flow_analysis_cache():
    """
    DEPRECATED: Use BoardQueryService.clear_cache() instead.

    Backward compatibility wrapper for existing code.
    """
    import asyncio
    asyncio.create_task(board_query_service.clear_cache())


async def collect_whole_flow_from_all_vtubers(current_time_str: str, previous_time_str: str):
    """
    DEPRECATED: Use BoardQueryService.get_whole_flow_from_all_vtubers() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_whole_flow_from_all_vtubers(
        current_time_str, previous_time_str
    )


async def collect_whole_info_from_all_vtubers():
    """
    DEPRECATED: Use BoardQueryService.get_whole_info_from_all_vtubers() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_whole_info_from_all_vtubers()


async def collect_all_dahanghai_and_follower_rise_num_stuff(recent_days: int = 1):
    """
    DEPRECATED: Use BoardQueryService.get_all_dahanghai_and_follower_rise_num_data() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_all_dahanghai_and_follower_rise_num_data(recent_days)


async def collect_all_live_status_stuff():
    """
    DEPRECATED: Use BoardQueryService.get_all_live_status_data() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_all_live_status_data()


async def collect_all_dahanghai_and_follower_stuff():
    """
    DEPRECATED: Use BoardQueryService.get_all_dahanghai_and_follower_data() instead.

    Backward compatibility wrapper for existing code.
    """
    return await board_query_service.get_all_dahanghai_and_follower_data()
