import asyncio

from vups_server.query.query_vup_user_data import *
from vups.logger import logger


async def test_queries():
    test_mid = "401315430"
    name = "星瞳"
    logger.info(f"test mid: {test_mid}")
    start_time = "2024-10-31"
    end_time = "2025-05-20"
    current_time = "2025-05-16"

    # try:
    #     logger.info(f"current stat: {dict(await query_current_stat_by_mid(test_mid))}")
    # except Exception as e:
    #     logger.error(f"Error in query_current_time_by_mid: {e}")

    try:
        logger.info(f"latest dynamic: {await user_content_service.get_latest_dynamic(test_mid)}")
    except Exception as e:
        logger.error(f"Error in query_latest_dynamic: {e}")


    # try: # pass
    #     logger.info(f"whole_user_all_stat: {await query_whole_user_all_stat_by_uid_and_recent(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_whole_user_all_stat_by_uid_and_recent: {e}")
    # try: # pass
    #     logger.info(f"query_whole_user_follower_num_by_mid_and_recent: {await query_whole_user_follower_num_by_mid_and_recent(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_whole_user_follower_num_by_mid_and_recent: {e}")

    # try: # pass
    #     logger.info(f"query_now_user_follower_num_by_mid: {await query_now_user_follower_num_by_mid(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_now_user_follower_num_by_mid: {e}")
    # try: # pass
    #     logger.info(f"query_whole_dahanghai_num_by_mid_and_recent: {await query_whole_dahanghai_num_by_mid_and_recent(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_whole_dahanghai_num_by_mid_and_recent: {e}")
    # try: # pass
    #     logger.info(f"query_now_user_dahanghai_num_by_mid: {await query_now_user_dahanghai_num_by_mid(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_now_user_dahanghai_num_by_mid: {e}")
    # try: # pass
    #     logger.info(f"query_user_info_by_mid: {await query_user_info_by_mid(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_user_info_by_mid: {e}")

    # try: # pass
    #     logger.info(f"calculate_dahanghai_rate_by_mid: {await calculate_dahanghai_rate_by_mid(test_mid, 2)}")
    # except Exception as e:
    #     logger.error(f"Error in calculate_dahanghai_rate_by_mid: {e}")
    # try: # pass
    #     logger.info(f"calculate_follower_rate_by_mid: {await calculate_follower_rate_by_mid(test_mid, 2)}")
    # except Exception as e:
    #     logger.error(f"Error in calculate_follower_rate_by_mid: {e}")
    # try: # pass
    #     logger.info(f"query_followers_review_rate: {await query_followers_review_rate(test_mid, '2025-05-19', 7)}")
    # except Exception as e:
    #     logger.error(f"Error in query_followers_review_rate: {e}")

    # try: # pass
    #     logger.info(f"query_user_dynamics_by_mid: {await query_user_dynamics_by_mid(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_user_dynamics_by_mid: {e}")
    # try: # pass
    #     logger.info(f"query_all_video_list_by_mid: {await query_all_video_list_by_mid(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_all_video_list_by_mid: {e}")

    # try: # pass
    #     logger.info(f"query_top_n_comments: {await query_top_n_comments(test_mid, start_time, end_time, 3)}")
    # except Exception as e:
    #     logger.error(f"Error in query_top_n_comments: {e}")
    # try: # pass
    #     logger.info(f"query_top_n_comments_user: {await query_top_n_comments_user(test_mid, start_time, end_time, 3)}")
    # except Exception as e:
    #     logger.error(f"Error in query_top_n_comments_user: {e}")
    # try: # pass
    #     logger.info(f"query_top_n_dynamics: {await query_top_n_dynamics(test_mid, start_time, end_time, 3)}")
    # except Exception as e:
    #     logger.error(f"Error in query_top_n_dynamics: {e}")
    # try: # pass
    #     logger.info(f"query_current_videos: {await query_current_videos(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_current_videos: {e}")
    # try: # pass
    #     logger.info(f"query_current_dynamics: {await query_current_dynamics(test_mid)}") # pass
    # except Exception as e:
    #     logger.error(f"Error in query_current_dynamics: {e}")
    # try: # pass
    #     logger.info(f"query_recent_top_n_videos: {await query_recent_top_n_videos(test_mid, 250, 3)}")
    # except Exception as e:
    #     logger.error(f"Error in query_recent_top_n_videos: {e}")
    # try:  # pass
    #     logger.info(f"query_comments_for_wordcloud: {await query_comments_for_wordcloud(test_mid, start_time, end_time)}")
    # except Exception as e:
    #     logger.error(f"Error in query_comments_for_wordcloud: {e}")

    # try: # pass
    #     info = await query_tieba_whole(test_mid, '2024-05-01', '2024-05-30')
    #     logger.info(f"query_tieba_whole: {info[0]}")
    # except Exception as e:
    #     logger.error(f"Error in query_tieba_whole: {e}")

    # try: # pass
    #     info = await query_tieba_threads(test_mid, '2024-05-01', '2024-05-30')
    #     logger.info(f"query_tieba_threads: {info[0]}")
    # except Exception as e:
    #     logger.error(f"Error in query_tieba_threads: {e}")

    # try: # pass
    #     info = await query_all_video_comments_by_mid(test_mid)
    #     logger.info(f"query_all_video_comments_by_mid: {info[0]}")
    # except Exception as e:
    #     logger.error(f"Error in query_all_video_comments_by_mid: {e}")
    # try: # pass
    #     logger.info(f"query_all_dynamics_comments_by_mid: {await query_all_dynamics_comments_by_mid(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_all_dynamics_comments_by_mid: {e}")

    # try: # pass
    #     logger.info(f"query_target_video_day_views: {await query_target_video_day_views(test_mid, 'BV1iSVqzDE2b', '2025-05-08')}")
    # except Exception as e:
    #     logger.error(f"Error in query_target_video_day_views: {e}")
    # try: # pass
    #     logger.info(f"query_recent_relationships: {await query_recent_relationships(test_mid, current_time, 100)}")
    # except Exception as e:
    #     logger.error(f"Error in query_recent_relationships: {e}")
    # try: # pass
    #     logger.info(f"query_tieba_summaries_from_ai_gen_table: {await query_tieba_summaries_from_ai_gen_table(test_mid, 265)}")
    # except Exception as e:
    #     logger.error(f"Error in query_tieba_summaries_from_ai_gen_table: {e}")
    # try: # pass
    #     logger.info(f"query_tieba_summaries_from_ai_gen_table_by_date: {await query_tieba_summaries_from_ai_gen_table_by_date(test_mid, current_time, 265)}")
    # except Exception as e:
    #     logger.error(f"Error in query_tieba_summaries_from_ai_gen_table_by_date: {e}")
    # try: # pass
    #     logger.info(f"query_rise_reason_from_ai_gen_table: {await query_rise_reason_from_ai_gen_table(test_mid, 30)}")
    # except Exception as e:
    #     logger.error(f"Error in query_rise_reason_from_ai_gen_table: {e}")

    # try: # pass
    #     logger.info(f"query_latest_fans_medal_rank: {await query_latest_fans_medal_rank(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_latest_fans_medal_rank: {e}")
    # try: # pass
    #     logger.info(f"query_fans_medal_rank_by_datetime: {await query_fans_medal_rank_by_datetime(test_mid, '2025-05-14')}")
    # except Exception as e:
    #     logger.error(f"Error in query_fans_medal_rank_by_datetime: {e}")
    # try: # pass
    #     logger.info(f"query_video_ai_conclusion_by_bvid: {await query_video_ai_conclusion_by_bvid('BV1iSVqzDE2b')}")
    # except Exception as e:
    #     logger.error(f"Error in query_video_ai_conclusion_by_bvid: {e}")

    # try: # pass
    #     logger.info(f"query_single_video_day_data: {await query_single_video_day_data('BV1iSVqzDE2b', '2025-05-08')}")
    # except Exception as e:
    #     logger.error(f"Error in query_single_video_day_data: {e}")
    # try: # pass
    #     logger.info(f"query_single_video_rencent_day_data: {await query_single_video_rencent_day_data('BV1iSVqzDE2b', 3 )}")
    # except Exception as e:
    #     logger.error(f"Error in query_single_video_rencent_day_data: {e}")

    # try: # pass
    #     logger.info(f"query_single_video_target_day_data: {await query_single_video_target_day_data('BV1E8R2Y3EJY', '2025-05-09')}")
    # except Exception as e:
    #     logger.error(f"Error in query_single_video_target_day_data: {e}")
    # try: # pass
    #     logger.info(f"query_top_n_view_rise_target_day_data: {await query_top_n_view_rise_target_day_data(test_mid, '2025-05-09', 10)}")
    # except Exception as e:
    #     logger.error(f"Error in query_top_n_view_rise_target_day_data: {e}")
    # try: # pass
    #     logger.info(f"query_top_n_view_rise_recent_day_data: {await query_top_n_view_rise_recent_day_data(test_mid, 10)}")
    # except Exception as e:
    #     logger.error(f"Error in query_top_n_view_rise_recent_day_data: {e}")

    # try:  # pass
    #     logger.info(f"query_top_n_view_rise_day_data_period: {await query_top_n_view_rise_day_data_period(test_mid, start_time, end_time, 10)}")
    # except Exception as e:
    #     logger.error(f"Error in query_top_n_view_rise_day_data_period: {e}")
    # try:  # pass
    #     logger.info(f"query_current_follower_change_num: {await query_current_follower_change_num(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_current_follower_change_num: {e}")
    # try: # pass
    #     logger.info(f"query_current_dahanghai_change_num: {await query_current_dahanghai_change_num(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_current_dahanghai_change_num: {e}")
    # try:  # pass
    #     logger.info(f"query_latest_dahanghai_list_by_uid: {await query_latest_dahanghai_list_by_uid(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_latest_dahanghai_list_by_uid: {e}")
    # try: # pass
    #     logger.info(f"query_dahanghai_list_by_uid_and_datetime: {await query_dahanghai_list_by_uid_and_datetime(test_mid, '2025-05-14')}")
    # except Exception as e:
    #     logger.error(f"Error in query_dahanghai_list_by_uid_and_datetime: {e}")

    # try: # pass
    #     info = await query_followers_list(test_mid, '2025-05-19', 30)
    #     logger.info(f"query_followers_list: {info[0]}")
    # except Exception as e:
    #     logger.error(f"Error in query_followers_list: {e}")

    # try: # pass
    #     logger.info(f"query_recent_comments_sentiment_value: {await query_recent_comments_sentiment_value(test_mid, '2025-05-16', 7, source = 'all')}")
    # except Exception as e:
    #     logger.error(f"Error in query_recent_comments_sentiment_value: {e}")

    # try:  # pass
    #     logger.info(f"query_comment_wordcloud: {await query_comment_wordcloud(test_mid, '星瞳', start_time, end_time)}")
    # except Exception as e:
    #     logger.error(f"Error in query_comment_wordcloud: {e}")

    # try: # pass
    #     logger.info(f"query_recent_info: {await query_recent_info(test_mid)}")
    # except Exception as e:
    #     logger.error(f"Error in query_recent_info: {e}")


async def main():
    await test_queries()

if __name__ ==  "__main__":
    asyncio.run(main())
