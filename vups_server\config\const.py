import pathlib

VUP_SERVER_ROOT = pathlib.Path(__file__).parent.parent.resolve()

# SQL
EXTERNAL_PGSQL = False
# PGSQL_HOST = "localhost" # dev
PGSQL_HOST = "***************"
PGSQL_PORT = 5432

PGSQL_DB = "postgres"
PGSQL_USER = "postgres"
PGSQL_PASSWORD = "Password123@vupbi"

PGSQL_CONFIG = {
    "host": PGSQL_HOST,
    "port": PGSQL_PORT,
    "database": PGSQL_DB,
    "user": PGSQL_USER,
    "password": PGSQL_PASSWORD,
    "ssl": False,
    "timeout": 30,
    "command_timeout": 60.0,
    "min_size": 10,
    "max_size": 50,
    "max_inactive_connection_lifetime": 300,
    "max_queries": 50000,
}

BDUSS = "BsTkhjcXp1UjRjeGdLR251dDdReUFYMkZCTFVtd3pYOU1TdkNDdmZGeEQtMDluSVFBQUFBJCQAAAAAAAAAAAEAAACAzNA00-NpaWkxMjEzOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAENuKGdDbihnTz"

DEFAULT_DELAYS = {
    "DYNAMICS_FETCH_DELAY": 10,
    "VIDEO_FETCH_DELAY": 3,
    "ALL_VIDEO_INFO_DELAY": 1,
    "VIDEO_AI_CONCLUSION_DELAY": 1,
    "TIEBA_WHOLE_DELAY": 1,
    "TIEBA_THREAD_DELAY": 0.5,
    "DAHANGHAI_LIST_FETCH_LIST_DELAY": 2,
    "FOLLOWER_LIST_FETCH_LIST_DELAY": 10,
    "FETCH_HISTORY_F_AND_D_DELAY": 10,
    "USER_CUR_DATE_DELAY": 8,
    "SQL_FAIL_DELAY": 3
}

DEFAULT_POOL_SIZES = {
    "TIEBA_INSERT_POOL_SIZE": 50,
    "COMMENT_INSERT_POOL_SIZE": 50,
    "FOLLOWER_REVIEW_INSERT_POOL_SIZE": 50
}

DEFAULT_LIMITS = {
    "FETCH_FOLLOWERS_MAX_PAGES": 20
}

DEFAULT_AI_CONFIG = {
    "AI_GEN_MODEL": "hunyuan-standard-256K"
}
