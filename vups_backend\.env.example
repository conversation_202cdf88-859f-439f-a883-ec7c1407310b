# VUPS Backend Configuration

# Server Configuration
VUPS_SERVER_HOST=0.0.0.0
VUPS_SERVER_PORT=8080
VUPS_SERVER_MODE=release

# Redis Configuration
VUPS_REDIS_HOST=localhost
VUPS_REDIS_PORT=6379
VUPS_REDIS_PASSWORD=
VUPS_REDIS_DB=0

# Celery Configuration
VUPS_CELERY_BROKER=redis://localhost:6379/0
VUPS_CELERY_BACKEND=redis://localhost:6379/0

# Python Configuration
VUPS_PYTHON_EXECUTABLE=python3
VUPS_PYTHON_SCRIPT_PATH=./
VUPS_PYTHON_TIMEOUT=300s
VUPS_PYTHON_MAX_WORKERS=5

# Logging Configuration
VUPS_LOGGING_LEVEL=info
VUPS_LOGGING_FORMAT=json
VUPS_LOGGING_OUTPUT=stdout
VUPS_LOGGING_FILENAME=logs/vups_backend.log

# Rate Limiting Configuration
VUPS_RATE_LIMIT_ENABLED=true
VUPS_RATE_LIMIT_RATE=100.0
VUPS_RATE_LIMIT_BURST=200

# CORS Configuration
VUPS_CORS_ALLOW_ORIGINS=*
VUPS_CORS_ALLOW_CREDENTIALS=true

# Database Configuration (if needed for direct DB access)
DATABASE_URL=postgresql://user:password@localhost:5432/vups_db

# External API Keys (if needed)
DASHSCOPE_API_KEY=your_dashscope_api_key_here
BILIBILI_SESSDATA=your_bilibili_sessdata_here

# Monitoring Configuration
PROMETHEUS_ENABLED=false
GRAFANA_ENABLED=false

# Security Configuration
JWT_SECRET=your_jwt_secret_here
API_KEY=your_api_key_here

# Development Configuration
DEBUG=false
PROFILE=false
